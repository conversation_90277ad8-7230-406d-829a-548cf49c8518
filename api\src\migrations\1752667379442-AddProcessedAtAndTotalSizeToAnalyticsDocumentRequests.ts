import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddProcessedAtAndTotalSizeToAnalyticsDocumentRequests1752667379442
	implements MigrationInterface
{
	name = 'AddProcessedAtAndTotalSizeToAnalyticsDocumentRequests1752667379442';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`
			ALTER TABLE "analytics_document_requests" 
			ADD COLUMN "total_size" bigint DEFAULT 0
		`);

		await queryRunner.query(`
			ALTER TABLE "analytics_document_requests" 
			ADD COLUMN "processed_at" timestamp
		`);
	}

	public async down(queryRunner: Query<PERSON>unner): Promise<void> {
		await queryRunner.query(`
			ALTER TABLE "analytics_document_requests" 
			DROP COLUMN "processed_at"
		`);

		await queryRunner.query(`
			ALTER TABLE "analytics_document_requests" 
			DROP COLUMN "total_size"
		`);
	}
}
