"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsMonitoringService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const winston_logger_service_1 = require("../../utils/logger/winston-logger.service");
const analytics_document_request_entity_1 = require("../entities/analytics-document-request.entity");
const redis_service_1 = require("../../utils/redis/redis.service");
let AnalyticsMonitoringService = class AnalyticsMonitoringService {
    constructor(logger, analyticsDocumentRequestRepository, redisService) {
        this.logger = logger;
        this.analyticsDocumentRequestRepository = analyticsDocumentRequestRepository;
        this.redisService = redisService;
        this.RATE_LIMIT_KEY_PREFIX = 'analytics_rate_limit';
        this.METRICS_CACHE_KEY = 'analytics_metrics_cache';
        this.CACHE_TTL = 300; // 5 minutes
    }
    /**
     * Record performance metrics for analytics document processing
     */
    async recordPerformanceMetrics(requestId, metrics) {
        try {
            const metricsKey = `analytics_performance:${requestId}`;
            const metricsData = {
                ...metrics,
                timestamp: new Date().toISOString(),
                requestId
            };
            // Store metrics in Redis with 7-day expiration
            await this.redisService.getClient().set(metricsKey, JSON.stringify(metricsData), 'EX', 7 * 24 * 60 * 60);
            // Log performance metrics
            this.logger.log('Analytics performance metrics recorded', {
                requestId,
                processingTimeMs: metrics.processingTimeMs,
                documentCount: metrics.documentCount,
                totalSizeBytes: metrics.totalSizeBytes,
                throughputDocsPerSecond: metrics.documentCount / (metrics.processingTimeMs / 1000)
            });
        }
        catch (error) {
            this.logger.error('Failed to record performance metrics', {
                requestId,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }
    /**
     * Get comprehensive analytics metrics
     */
    async getAnalyticsMetrics() {
        try {
            // Try to get cached metrics first
            const cachedMetrics = await this.redisService.get(this.METRICS_CACHE_KEY);
            if (cachedMetrics) {
                return JSON.parse(cachedMetrics);
            }
            // Calculate metrics from database
            const metrics = await this.calculateMetrics();
            // Cache the metrics
            await this.redisService.getClient().set(this.METRICS_CACHE_KEY, JSON.stringify(metrics), 'EX', this.CACHE_TTL);
            return metrics;
        }
        catch (error) {
            this.logger.error('Failed to get analytics metrics', {
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Check rate limiting for analytics document requests
     */
    async checkRateLimit(userId, clinicId) {
        try {
            const userKey = `${this.RATE_LIMIT_KEY_PREFIX}:user:${userId}`;
            const clinicKey = `${this.RATE_LIMIT_KEY_PREFIX}:clinic:${clinicId}`;
            // Rate limits: 10 requests per hour per user, 50 requests per hour per clinic
            const userLimit = 100;
            const clinicLimit = 50;
            const windowSeconds = 3600; // 1 hour
            // Check user rate limit
            const userCount = await this.incrementRateLimit(userKey, windowSeconds);
            const userAllowed = userCount <= userLimit;
            // Check clinic rate limit
            const clinicCount = await this.incrementRateLimit(clinicKey, windowSeconds);
            const clinicAllowed = clinicCount <= clinicLimit;
            const allowed = userAllowed && clinicAllowed;
            const remaining = Math.min(Math.max(0, userLimit - userCount), Math.max(0, clinicLimit - clinicCount));
            const resetTime = new Date(Date.now() + windowSeconds * 1000);
            if (!allowed) {
                this.logger.warn('Analytics rate limit exceeded', {
                    userId,
                    clinicId,
                    userCount,
                    clinicCount,
                    userLimit,
                    clinicLimit
                });
            }
            return { allowed, remaining, resetTime };
        }
        catch (error) {
            this.logger.error('Failed to check rate limit', {
                userId,
                clinicId,
                error: error instanceof Error ? error.message : String(error)
            });
            // Allow request if rate limiting fails
            return { allowed: true, remaining: 0, resetTime: new Date() };
        }
    }
    /**
     * Get performance metrics for a specific request
     */
    async getRequestPerformanceMetrics(requestId) {
        try {
            const metricsKey = `analytics_performance:${requestId}`;
            const metricsData = await this.redisService.get(metricsKey);
            if (!metricsData) {
                return null;
            }
            const metrics = JSON.parse(metricsData);
            delete metrics.timestamp;
            delete metrics.requestId;
            return metrics;
        }
        catch (error) {
            this.logger.error('Failed to get request performance metrics', {
                requestId,
                error: error instanceof Error ? error.message : String(error)
            });
            return null;
        }
    }
    /**
     * Get system health metrics for analytics processing
     */
    async getSystemHealthMetrics() {
        try {
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
            // Get recent requests
            const recentRequests = await this.analyticsDocumentRequestRepository.find({
                where: {
                    createdAt: oneHourAgo
                },
                select: ['status', 'createdAt', 'processedAt']
            });
            const activeRequests = recentRequests.filter(r => r.status === analytics_document_request_entity_1.AnalyticsDocumentStatus.PENDING ||
                r.status === analytics_document_request_entity_1.AnalyticsDocumentStatus.PROCESSING).length;
            const completedRequests = recentRequests.filter(r => r.status === analytics_document_request_entity_1.AnalyticsDocumentStatus.COMPLETED ||
                r.status === analytics_document_request_entity_1.AnalyticsDocumentStatus.FAILED);
            const failedRequests = recentRequests.filter(r => r.status === analytics_document_request_entity_1.AnalyticsDocumentStatus.FAILED).length;
            const failureRate = completedRequests.length > 0 ?
                (failedRequests / completedRequests.length) * 100 : 0;
            // Calculate average processing time
            const processedRequests = completedRequests.filter(r => r.processedAt);
            const averageProcessingTime = processedRequests.length > 0 ?
                processedRequests.reduce((sum, r) => {
                    const processingTime = r.processedAt.getTime() - r.createdAt.getTime();
                    return sum + processingTime;
                }, 0) / processedRequests.length : 0;
            // Determine system status
            let status = 'healthy';
            if (failureRate > 20 || averageProcessingTime > 300000) { // 5 minutes
                status = 'unhealthy';
            }
            else if (failureRate > 10 || averageProcessingTime > 180000) { // 3 minutes
                status = 'degraded';
            }
            return {
                status,
                metrics: {
                    activeRequests,
                    failureRate,
                    averageProcessingTime,
                    queueDepth: activeRequests // Simplified queue depth
                }
            };
        }
        catch (error) {
            this.logger.error('Failed to get system health metrics', {
                error: error instanceof Error ? error.message : String(error)
            });
            return {
                status: 'unhealthy',
                metrics: {
                    activeRequests: 0,
                    failureRate: 100,
                    averageProcessingTime: 0,
                    queueDepth: 0
                }
            };
        }
    }
    async incrementRateLimit(key, windowSeconds) {
        var _a;
        const client = this.redisService.getClient();
        // Use Redis pipeline for atomic operations
        const pipeline = client.pipeline();
        pipeline.incr(key);
        pipeline.expire(key, windowSeconds);
        const results = await pipeline.exec();
        return ((_a = results === null || results === void 0 ? void 0 : results[0]) === null || _a === void 0 ? void 0 : _a[1]) || 0;
    }
    async calculateMetrics() {
        const now = new Date();
        const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        // Use database-level aggregation for better performance
        // 1. Get total requests count
        const totalRequests = await this.analyticsDocumentRequestRepository.count();
        // 2. Get requests by status using GROUP BY
        const statusResults = await this.analyticsDocumentRequestRepository
            .createQueryBuilder('request')
            .select('request.status', 'status')
            .addSelect('COUNT(*)', 'count')
            .groupBy('request.status')
            .getRawMany();
        const requestsByStatus = statusResults.reduce((acc, result) => {
            acc[result.status] = parseInt(result.count);
            return acc;
        }, {});
        // 3. Calculate average processing time using database aggregation
        const processingTimeResult = await this.analyticsDocumentRequestRepository
            .createQueryBuilder('request')
            .select('AVG(EXTRACT(EPOCH FROM (request.processedAt - request.createdAt)) * 1000)', 'avgProcessingTime')
            .addSelect('COUNT(*)', 'processedCount')
            .where('request.processedAt IS NOT NULL')
            .getRawOne();
        const averageProcessingTime = (processingTimeResult === null || processingTimeResult === void 0 ? void 0 : processingTimeResult.avgProcessingTime)
            ? parseFloat(processingTimeResult.avgProcessingTime)
            : 0;
        // 4. Calculate success and error rates from status counts
        const completedRequests = requestsByStatus[analytics_document_request_entity_1.AnalyticsDocumentStatus.COMPLETED] || 0;
        const failedRequests = requestsByStatus[analytics_document_request_entity_1.AnalyticsDocumentStatus.FAILED] || 0;
        const totalCompleted = completedRequests + failedRequests;
        const successRate = totalCompleted > 0 ? (completedRequests / totalCompleted) * 100 : 0;
        const errorRate = totalCompleted > 0 ? (failedRequests / totalCompleted) * 100 : 0;
        // 5. Get recent requests counts using database queries
        const requestsLast24Hours = await this.analyticsDocumentRequestRepository
            .createQueryBuilder('request')
            .where('request.createdAt >= :oneDayAgo', { oneDayAgo })
            .getCount();
        const requestsLast7Days = await this.analyticsDocumentRequestRepository
            .createQueryBuilder('request')
            .where('request.createdAt >= :sevenDaysAgo', { sevenDaysAgo })
            .getCount();
        // 6. Calculate average file size using database aggregation
        const fileSizeResult = await this.analyticsDocumentRequestRepository
            .createQueryBuilder('request')
            .select('AVG(request.totalSize)', 'avgFileSize')
            .where('request.totalSize > 0')
            .getRawOne();
        const averageFileSize = (fileSizeResult === null || fileSizeResult === void 0 ? void 0 : fileSizeResult.avgFileSize)
            ? parseFloat(fileSizeResult.avgFileSize)
            : 0;
        // 7. Get peak hours using database aggregation
        const peakHoursResults = await this.analyticsDocumentRequestRepository
            .createQueryBuilder('request')
            .select('EXTRACT(HOUR FROM request.createdAt)', 'hour')
            .addSelect('COUNT(*)', 'count')
            .groupBy('EXTRACT(HOUR FROM request.createdAt)')
            .orderBy('COUNT(*)', 'DESC')
            .limit(5)
            .getRawMany();
        const peakHours = peakHoursResults.map(result => ({
            hour: parseInt(result.hour),
            count: parseInt(result.count)
        }));
        // 8. Get top document types using database aggregation
        const documentTypeResults = await this.analyticsDocumentRequestRepository
            .createQueryBuilder('request')
            .select('request.documentType', 'type')
            .addSelect('COUNT(*)', 'count')
            .groupBy('request.documentType')
            .orderBy('COUNT(*)', 'DESC')
            .getRawMany();
        const topDocumentTypes = documentTypeResults.map(result => ({
            type: result.type,
            count: parseInt(result.count)
        }));
        return {
            totalRequests,
            requestsByStatus,
            averageProcessingTime,
            successRate,
            errorRate,
            requestsLast24Hours,
            requestsLast7Days,
            averageFileSize,
            peakHours,
            topDocumentTypes
        };
    }
};
exports.AnalyticsMonitoringService = AnalyticsMonitoringService;
exports.AnalyticsMonitoringService = AnalyticsMonitoringService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, typeorm_1.InjectRepository)(analytics_document_request_entity_1.AnalyticsDocumentRequestEntity)),
    __metadata("design:paramtypes", [winston_logger_service_1.WinstonLogger,
        typeorm_2.Repository,
        redis_service_1.RedisService])
], AnalyticsMonitoringService);
//# sourceMappingURL=analytics-monitoring.service.js.map