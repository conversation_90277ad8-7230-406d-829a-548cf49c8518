import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WinstonLogger } from '../../utils/logger/winston-logger.service';
import { AnalyticsDocumentRequestEntity, AnalyticsDocumentStatus } from '../entities/analytics-document-request.entity';
import { RedisService } from '../../utils/redis/redis.service';

export interface AnalyticsMetrics {
	totalRequests: number;
	requestsByStatus: Record<AnalyticsDocumentStatus, number>;
	averageProcessingTime: number;
	successRate: number;
	errorRate: number;
	requestsLast24Hours: number;
	requestsLast7Days: number;
	averageFileSize: number;
	peakHours: { hour: number; count: number }[];
	topDocumentTypes: { type: string; count: number }[];
}

export interface PerformanceMetrics {
	processingTimeMs: number;
	documentCount: number;
	totalSizeBytes: number;
	pdfGenerationTimeMs?: number;
	excelGenerationTimeMs?: number;
	s3UploadTimeMs?: number;
	emailSendTimeMs?: number;
}

@Injectable()
export class AnalyticsMonitoringService {
	private readonly RATE_LIMIT_KEY_PREFIX = 'analytics_rate_limit';
	private readonly METRICS_CACHE_KEY = 'analytics_metrics_cache';
	private readonly CACHE_TTL = 300; // 5 minutes

	constructor(
		private readonly logger: WinstonLogger,
		@InjectRepository(AnalyticsDocumentRequestEntity)
		private readonly analyticsDocumentRequestRepository: Repository<AnalyticsDocumentRequestEntity>,
		private readonly redisService: RedisService
	) {}

	/**
	 * Record performance metrics for analytics document processing
	 */
	async recordPerformanceMetrics(requestId: string, metrics: PerformanceMetrics): Promise<void> {
		try {
			const metricsKey = `analytics_performance:${requestId}`;
			const metricsData = {
				...metrics,
				timestamp: new Date().toISOString(),
				requestId
			};

			// Store metrics in Redis with 7-day expiration
			await this.redisService.getClient().set(metricsKey, JSON.stringify(metricsData), 'EX', 7 * 24 * 60 * 60);

			// Log performance metrics
			this.logger.log('Analytics performance metrics recorded', {
				requestId,
				processingTimeMs: metrics.processingTimeMs,
				documentCount: metrics.documentCount,
				totalSizeBytes: metrics.totalSizeBytes,
				throughputDocsPerSecond: metrics.documentCount / (metrics.processingTimeMs / 1000)
			});

		} catch (error) {
			this.logger.error('Failed to record performance metrics', {
				requestId,
				error: error instanceof Error ? error.message : String(error)
			});
		}
	}

	/**
	 * Get comprehensive analytics metrics
	 */
	async getAnalyticsMetrics(): Promise<AnalyticsMetrics> {
		try {
			// Try to get cached metrics first
			const cachedMetrics = await this.redisService.get(this.METRICS_CACHE_KEY);
			if (cachedMetrics) {
				return JSON.parse(cachedMetrics);
			}

			// Calculate metrics from database
			const metrics = await this.calculateMetrics();

			// Cache the metrics
			await this.redisService.getClient().set(this.METRICS_CACHE_KEY, JSON.stringify(metrics), 'EX', this.CACHE_TTL);

			return metrics;

		} catch (error) {
			this.logger.error('Failed to get analytics metrics', {
				error: error instanceof Error ? error.message : String(error)
			});
			throw error;
		}
	}

	/**
	 * Check rate limiting for analytics document requests
	 */
	async checkRateLimit(userId: string, clinicId: string): Promise<{
		allowed: boolean;
		remaining: number;
		resetTime: Date;
	}> {
		try {
			const userKey = `${this.RATE_LIMIT_KEY_PREFIX}:user:${userId}`;
			const clinicKey = `${this.RATE_LIMIT_KEY_PREFIX}:clinic:${clinicId}`;

			// Rate limits: 10 requests per hour per user, 50 requests per hour per clinic
			const userLimit = 100;
			const clinicLimit = 50;
			const windowSeconds = 3600; // 1 hour

			// Check user rate limit
			const userCount = await this.incrementRateLimit(userKey, windowSeconds);
			const userAllowed = userCount <= userLimit;

			// Check clinic rate limit
			const clinicCount = await this.incrementRateLimit(clinicKey, windowSeconds);
			const clinicAllowed = clinicCount <= clinicLimit;

			const allowed = userAllowed && clinicAllowed;
			const remaining = Math.min(
				Math.max(0, userLimit - userCount),
				Math.max(0, clinicLimit - clinicCount)
			);

			const resetTime = new Date(Date.now() + windowSeconds * 1000);

			if (!allowed) {
				this.logger.warn('Analytics rate limit exceeded', {
					userId,
					clinicId,
					userCount,
					clinicCount,
					userLimit,
					clinicLimit
				});
			}

			return { allowed, remaining, resetTime };

		} catch (error) {
			this.logger.error('Failed to check rate limit', {
				userId,
				clinicId,
				error: error instanceof Error ? error.message : String(error)
			});
			// Allow request if rate limiting fails
			return { allowed: true, remaining: 0, resetTime: new Date() };
		}
	}

	/**
	 * Get performance metrics for a specific request
	 */
	async getRequestPerformanceMetrics(requestId: string): Promise<PerformanceMetrics | null> {
		try {
			const metricsKey = `analytics_performance:${requestId}`;
			const metricsData = await this.redisService.get(metricsKey);

			if (!metricsData) {
				return null;
			}

			const metrics = JSON.parse(metricsData);
			delete metrics.timestamp;
			delete metrics.requestId;

			return metrics;

		} catch (error) {
			this.logger.error('Failed to get request performance metrics', {
				requestId,
				error: error instanceof Error ? error.message : String(error)
			});
			return null;
		}
	}

	/**
	 * Get system health metrics for analytics processing
	 */
	async getSystemHealthMetrics(): Promise<{
		status: 'healthy' | 'degraded' | 'unhealthy';
		metrics: {
			activeRequests: number;
			failureRate: number;
			averageProcessingTime: number;
			queueDepth: number;
		};
	}> {
		try {
			const now = new Date();
			const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

			// Get recent requests
			const recentRequests = await this.analyticsDocumentRequestRepository.find({
				where: {
					createdAt: oneHourAgo
				},
				select: ['status', 'createdAt', 'processedAt']
			});

			const activeRequests = recentRequests.filter(r => 
				r.status === AnalyticsDocumentStatus.PENDING || 
				r.status === AnalyticsDocumentStatus.PROCESSING
			).length;

			const completedRequests = recentRequests.filter(r => 
				r.status === AnalyticsDocumentStatus.COMPLETED ||
				r.status === AnalyticsDocumentStatus.FAILED
			);

			const failedRequests = recentRequests.filter(r => 
				r.status === AnalyticsDocumentStatus.FAILED
			).length;

			const failureRate = completedRequests.length > 0 ? 
				(failedRequests / completedRequests.length) * 100 : 0;

			// Calculate average processing time
			const processedRequests = completedRequests.filter(r => r.processedAt);
			const averageProcessingTime = processedRequests.length > 0 ?
				processedRequests.reduce((sum, r) => {
					const processingTime = r.processedAt!.getTime() - r.createdAt.getTime();
					return sum + processingTime;
				}, 0) / processedRequests.length : 0;

			// Determine system status
			let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
			if (failureRate > 20 || averageProcessingTime > 300000) { // 5 minutes
				status = 'unhealthy';
			} else if (failureRate > 10 || averageProcessingTime > 180000) { // 3 minutes
				status = 'degraded';
			}

			return {
				status,
				metrics: {
					activeRequests,
					failureRate,
					averageProcessingTime,
					queueDepth: activeRequests // Simplified queue depth
				}
			};

		} catch (error) {
			this.logger.error('Failed to get system health metrics', {
				error: error instanceof Error ? error.message : String(error)
			});
			return {
				status: 'unhealthy',
				metrics: {
					activeRequests: 0,
					failureRate: 100,
					averageProcessingTime: 0,
					queueDepth: 0
				}
			};
		}
	}

	private async incrementRateLimit(key: string, windowSeconds: number): Promise<number> {
		const client = this.redisService.getClient();
		
		// Use Redis pipeline for atomic operations
		const pipeline = client.pipeline();
		pipeline.incr(key);
		pipeline.expire(key, windowSeconds);
		
		const results = await pipeline.exec();
		return results?.[0]?.[1] as number || 0;
	}

	private async calculateMetrics(): Promise<AnalyticsMetrics> {
		const now = new Date();
		const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
		const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

		// Use database-level aggregation for better performance

		// 1. Get total requests count
		const totalRequests = await this.analyticsDocumentRequestRepository.count();

		// 2. Get requests by status using GROUP BY
		const statusResults = await this.analyticsDocumentRequestRepository
			.createQueryBuilder('request')
			.select('request.status', 'status')
			.addSelect('COUNT(*)', 'count')
			.groupBy('request.status')
			.getRawMany();

		const requestsByStatus = statusResults.reduce((acc, result) => {
			acc[result.status as AnalyticsDocumentStatus] = parseInt(result.count);
			return acc;
		}, {} as Record<AnalyticsDocumentStatus, number>);

		// 3. Calculate average processing time using database aggregation
		const processingTimeResult = await this.analyticsDocumentRequestRepository
			.createQueryBuilder('request')
			.select('AVG(EXTRACT(EPOCH FROM (request.processedAt - request.createdAt)) * 1000)', 'avgProcessingTime')
			.addSelect('COUNT(*)', 'processedCount')
			.where('request.processedAt IS NOT NULL')
			.getRawOne();

		const averageProcessingTime = processingTimeResult?.avgProcessingTime
			? parseFloat(processingTimeResult.avgProcessingTime)
			: 0;

		// 4. Calculate success and error rates from status counts
		const completedRequests = requestsByStatus[AnalyticsDocumentStatus.COMPLETED] || 0;
		const failedRequests = requestsByStatus[AnalyticsDocumentStatus.FAILED] || 0;
		const totalCompleted = completedRequests + failedRequests;
		const successRate = totalCompleted > 0 ? (completedRequests / totalCompleted) * 100 : 0;
		const errorRate = totalCompleted > 0 ? (failedRequests / totalCompleted) * 100 : 0;

		// 5. Get recent requests counts using database queries
		const requestsLast24Hours = await this.analyticsDocumentRequestRepository
			.createQueryBuilder('request')
			.where('request.createdAt >= :oneDayAgo', { oneDayAgo })
			.getCount();

		const requestsLast7Days = await this.analyticsDocumentRequestRepository
			.createQueryBuilder('request')
			.where('request.createdAt >= :sevenDaysAgo', { sevenDaysAgo })
			.getCount();

		// 6. Calculate average file size using database aggregation
		const fileSizeResult = await this.analyticsDocumentRequestRepository
			.createQueryBuilder('request')
			.select('AVG(request.totalSize)', 'avgFileSize')
			.where('request.totalSize > 0')
			.getRawOne();

		const averageFileSize = fileSizeResult?.avgFileSize
			? parseFloat(fileSizeResult.avgFileSize)
			: 0;

		// 7. Get peak hours using database aggregation
		const peakHoursResults = await this.analyticsDocumentRequestRepository
			.createQueryBuilder('request')
			.select('EXTRACT(HOUR FROM request.createdAt)', 'hour')
			.addSelect('COUNT(*)', 'count')
			.groupBy('EXTRACT(HOUR FROM request.createdAt)')
			.orderBy('COUNT(*)', 'DESC')
			.limit(5)
			.getRawMany();

		const peakHours = peakHoursResults.map(result => ({
			hour: parseInt(result.hour),
			count: parseInt(result.count)
		}));

		// 8. Get top document types using database aggregation
		const documentTypeResults = await this.analyticsDocumentRequestRepository
			.createQueryBuilder('request')
			.select('request.documentType', 'type')
			.addSelect('COUNT(*)', 'count')
			.groupBy('request.documentType')
			.orderBy('COUNT(*)', 'DESC')
			.getRawMany();

		const topDocumentTypes = documentTypeResults.map(result => ({
			type: result.type,
			count: parseInt(result.count)
		}));

		return {
			totalRequests,
			requestsByStatus,
			averageProcessingTime,
			successRate,
			errorRate,
			requestsLast24Hours,
			requestsLast7Days,
			averageFileSize,
			peakHours,
			topDocumentTypes
		};
	}
}
