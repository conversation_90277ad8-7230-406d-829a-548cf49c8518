import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	CreateDateColumn,
	UpdateDateColumn,
	Index
} from 'typeorm';

export enum AnalyticsDocumentStatus {
	PENDING = 'PENDING',
	PROCESSING = 'PROCESSING',
	COMPLETED = 'COMPLETED',
	FAILED = 'FAILED',
	EXPIRED = 'EXPIRED'
}

export enum AnalyticsDocumentType {
	INVOICE = 'INVOICE',
	RECEIPT = 'RECEIPT',
	CREDIT_NOTE = 'CREDIT_NOTE'
}

export enum AnalyticsRecipientType {
	CLIENT = 'CLIENT',
	OTHER = 'OTHER'
}

@Entity('analytics_document_requests')
@Index(['clinicId', 'createdAt'])
@Index(['status', 'createdAt'])
@Index(['expiresAt'])
export class AnalyticsDocumentRequestEntity {
	@PrimaryGeneratedColumn('uuid')
	id!: string;

	@Column({ type: 'uuid', name: 'clinic_id' })
	clinicId!: string;

	@Column({ type: 'uuid', name: 'brand_id' })
	brandId!: string;

	@Column({ type: 'uuid', name: 'user_id' })
	userId!: string;

	@Column({
		type: 'enum',
		enum: AnalyticsDocumentType,
		name: 'document_type'
	})
	documentType!: AnalyticsDocumentType;

	@Column({
		type: 'enum',
		enum: AnalyticsRecipientType,
		name: 'recipient_type'
	})
	recipientType!: AnalyticsRecipientType;

	@Column({ type: 'varchar', name: 'recipient_email', nullable: true })
	recipientEmail?: string;

	@Column({ type: 'varchar', name: 'recipient_phone', nullable: true })
	recipientPhone?: string;

	@Column({ type: 'date', name: 'start_date' })
	startDate!: Date;

	@Column({ type: 'date', name: 'end_date' })
	endDate!: Date;

	@Column({
		type: 'enum',
		enum: AnalyticsDocumentStatus,
		name: 'status',
		default: AnalyticsDocumentStatus.PENDING
	})
	status!: AnalyticsDocumentStatus;

	@Column({ type: 'varchar', name: 'pdf_file_key', nullable: true })
	pdfFileKey?: string;

	@Column({ type: 'varchar', name: 'excel_file_key', nullable: true })
	excelFileKey?: string;

	@Column({ type: 'text', name: 'error_message', nullable: true })
	errorMessage?: string;

	@Column({ type: 'integer', name: 'document_count', default: 0 })
	documentCount!: number;

	@Column({ type: 'bigint', name: 'total_size', default: 0 })
	totalSize!: number;

	@Column({ type: 'timestamp', name: 'processed_at', nullable: true })
	processedAt?: Date;

	@Column({ type: 'jsonb', name: 'processing_metadata', nullable: true })
	processingMetadata?: {
		totalDocuments?: number;
		processedDocuments?: number;
		batchSize?: number;
		startedAt?: Date;
		completedAt?: Date;
	};

	@Column({
		type: 'timestamp',
		name: 'expires_at',
		default: () => "CURRENT_TIMESTAMP + INTERVAL '7 days'"
	})
	expiresAt!: Date;

	@CreateDateColumn({ name: 'created_at' })
	createdAt!: Date;

	@UpdateDateColumn({ name: 'updated_at' })
	updatedAt!: Date;
}
