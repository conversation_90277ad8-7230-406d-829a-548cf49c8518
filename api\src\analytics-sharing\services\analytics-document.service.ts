import { Injectable, NotFoundException } from '@nestjs/common';
import { ModuleRef } from '@nestjs/core';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, <PERSON>Than, In } from 'typeorm';
import { <PERSON><PERSON>ogger } from '../../utils/logger/winston-logger.service';
import { User } from '../../users/entities/user.entity';
import {
	AnalyticsDocumentRequestEntity,
	AnalyticsDocumentStatus,
	AnalyticsDocumentType,
	AnalyticsRecipientType
} from '../entities/analytics-document-request.entity';
import {
	AnalyticsDocumentRequest,
	AnalyticsDocumentProcessingResult,
	DEFAULT_BATCH_CONFIG,
	ExcelReportData,
	InvoiceExcelRow,
	ReceiptExcelRow,
	CreditNoteExcelRow
} from '../interfaces/analytics-sharing.interface';
import { InvoiceEntity } from '../../invoice/entities/invoice.entity';
import { PaymentDetailsEntity } from '../../payment-details/entities/payment-details.entity';
import { EnumAmountType } from '../../payment-details/enums/enum-credit-types';
import { EnumInvoiceType } from '../../invoice/enums/enum-invoice-types';

import { Patient } from '../../patients/entities/patient.entity';
import { OwnerBrand } from '../../owners/entities/owner-brand.entity';
import { ClinicEntity } from '../../clinics/entities/clinic.entity';

import { S3Service } from '../../utils/aws/s3/s3.service';
import { SqsService } from '../../utils/aws/sqs/sqs.service';
import { AnalyticsMonitoringService } from './analytics-monitoring.service';
import * as XLSX from 'xlsx';
import { generatePDFBuffer } from '../../utils/generatePdf';
import {
	generateInvoice as generateNewInvoice,
	InvoiceData
} from '../../utils/pdfs/new/generateInvoice';
import {
	generateCreditNote as generateNewCreditNote,
	CreditNoteData
} from '../../utils/pdfs/new/generateCreditNote';
import {
	generatePaymentReceipt as generateNewPaymentReceipt,
	ReceiptData
} from '../../utils/pdfs/new/generatePaymentReceipt';
import * as moment from 'moment';
import { SESMailService } from '../../utils/aws/ses/send-mail-service';
import { isProduction, isProductionOrUat } from '../../utils/common/get-login-url';
import { DEV_SES_EMAIL, ANALYTICS_BCC_EMAIL } from '../../utils/constants';


@Injectable()
export class AnalyticsDocumentService {
	constructor(
		private readonly logger: WinstonLogger,
		@InjectRepository(AnalyticsDocumentRequestEntity)
		private readonly analyticsDocumentRequestRepository: Repository<AnalyticsDocumentRequestEntity>,
		@InjectRepository(InvoiceEntity)
		private readonly invoiceRepository: Repository<InvoiceEntity>,
		@InjectRepository(PaymentDetailsEntity)
		private readonly paymentDetailsRepository: Repository<PaymentDetailsEntity>,
		@InjectRepository(Patient)
		private readonly patientRepository: Repository<Patient>,
		@InjectRepository(OwnerBrand)
		private readonly ownerBrandRepository: Repository<OwnerBrand>,
		@InjectRepository(ClinicEntity)
		private readonly clinicRepository: Repository<ClinicEntity>,
		@InjectRepository(User)
		private readonly userRepository: Repository<User>,

		private readonly s3Service: S3Service,
		private readonly analyticsMonitoringService: AnalyticsMonitoringService,
		private readonly moduleRef: ModuleRef,
		private readonly mailService: SESMailService
	) { }

	/**
	 * Send email with PDF and Excel attachments
	 */
	async sendMail(
		body: string,
		buffers: Buffer[],
		fileName: string[],
		email: string,
		subject?: string
	) {
		try {
			if (isProductionOrUat() && email) {
				await this.mailService.sendMail({
					body: body,
					subject: subject ?? 'Analytics Report',
					pdfBuffers: buffers,
					pdfFileNames: fileName,
					toMailAddress: email
				});
				this.logger.log('Production Mail sent successfully', {
					subject: subject ?? 'Analytics Report',
					pdfFileNames: fileName,
					toMailAddress: email
				});

				// Log successful email sending for debugging
				this.logger.debug('✅ ANALYTICS EMAIL SENT', {
					to: email,
					subject: subject ?? 'Analytics Report',
					attachments: fileName.join(', '),
					status: 'Production email sent successfully'
				});
			} else if (!isProduction()) {
				await this.mailService.sendMail({
					body: body,
					subject: subject ?? 'Analytics Report',
					pdfBuffers: buffers,
					pdfFileNames: fileName,
					toMailAddress: DEV_SES_EMAIL
				});
				this.logger.log('UAT Mail sent successfully', {
					subject: subject ?? 'Analytics Report',
					pdfFileNames: fileName,
					toMailAddress: DEV_SES_EMAIL
				});

				// Log successful email sending for debugging
				this.logger.debug('✅ ANALYTICS EMAIL SENT', {
					to: DEV_SES_EMAIL,
					subject: subject ?? 'Analytics Report',
					attachments: fileName.join(', '),
					status: 'UAT email sent successfully'
				});
			}
		} catch (error) {
			this.logger.error('Error sending email with attachments', {
				error: error instanceof Error ? error.message : String(error)
			});

			// Log email failure details for debugging
			this.logger.debug('❌ ANALYTICS EMAIL FAILED', {
				to: email,
				subject: subject || 'Analytics Report',
				body: body,
				attachments: fileName.join(', ') || 'None',
				error: error instanceof Error ? error.message : String(error)
			});

			throw error;
		}
	}

	/**
	 * Share analytics documents (PDF + Excel) via email
	 */
	async shareAnalyticsDocuments(
		request: AnalyticsDocumentRequest
	): Promise<string> {
		try {
			// Check rate limiting
			const rateLimitResult = await this.analyticsMonitoringService.checkRateLimit(
				request.userId,
				request.clinicId
			);

			if (!rateLimitResult.allowed) {
				throw new Error(
					`Rate limit exceeded. Please try again after ${rateLimitResult.resetTime.toISOString()}. Remaining requests: ${rateLimitResult.remaining}`
				);
			}

			// Resolve recipient email for CLIENT type
			let recipientEmail = request.recipientEmail;
			if (request.recipientType === AnalyticsRecipientType.CLIENT) {
				// For CLIENT type, automatically use the current user's email
				const user = await this.userRepository.findOne({
					where: { id: request.userId },
					select: { email: true }
				});
				if (!user) {
					throw new Error(`User with ID "${request.userId}" not found`);
				}
				recipientEmail = user.email;
			}

			// Log email details for debugging
			this.logger.log('📧 Analytics Document Email Details:', {
				recipientType: request.recipientType,
				recipientEmail: recipientEmail,
				documentType: request.documentType,
				dateRange: `${request.startDate.toISOString().split('T')[0]} to ${request.endDate.toISOString().split('T')[0]}`,
				requestId: request.requestId
			});

			// Create analytics document request record
			const analyticsRequest = this.analyticsDocumentRequestRepository.create({
				clinicId: request.clinicId,
				brandId: request.brandId,
				userId: request.userId,
				documentType: request.documentType,
				recipientType: request.recipientType,
				recipientEmail: recipientEmail,
				recipientPhone: request.recipientPhone,
				startDate: request.startDate,
				endDate: request.endDate,
				status: AnalyticsDocumentStatus.PENDING
			});

			// Save the entity and let the database generate the ID
			const savedRequest = await this.analyticsDocumentRequestRepository.save(analyticsRequest);

			// Queue the document processing for background execution (Phase 5)
			try {
				const sqsService = this.moduleRef.get(SqsService, { strict: false });
				await sqsService.sendMessage({
					queueKey: 'NidanaAnalyticsDocuments',
					messageBody: {
						data: {
							serviceType: 'processAnalyticsDocuments',
							requestId: savedRequest.id
						}
					},
					deduplicationId: `analytics-processing-${savedRequest.id}`
				});
			} catch (error) {
				this.logger.error('Failed to queue analytics document processing', {
					requestId: savedRequest.id,
					error: error instanceof Error ? error.message : String(error)
				});
				// Continue without queuing - the request is still saved
			}

			this.logger.log('Analytics document request created and queued for processing', {
				requestId: savedRequest.id,
				documentType: request.documentType,
				recipientType: request.recipientType,
				status: 'PENDING'
			});

			return savedRequest.id;
		} catch (error) {
			this.logger.error('Failed to create analytics document request', {
				error: error instanceof Error ? error.message : String(error)
			});
			throw error;
		}
	}

	/**
	 * Get analytics document request status
	 */
	async getAnalyticsDocumentStatus(requestId: string) {
		const request = await this.analyticsDocumentRequestRepository.findOne({
			where: { id: requestId }
		});

		if (!request) {
			throw new NotFoundException(`Analytics document request with ID ${requestId} not found`);
		}

		return {
			id: request.id,
			status: request.status,
			documentType: request.documentType,
			recipientType: request.recipientType,
			recipientEmail: request.recipientEmail,
			createdAt: request.createdAt,
			updatedAt: request.updatedAt,
			expiresAt: request.expiresAt,
			errorMessage: request.errorMessage,
			processedAt: request.processedAt,
			documentCount: request.documentCount,
			totalSize: request.totalSize
		};
	}

	/**
	 * Process analytics documents in background (called by SQS handler)
	 */
	async processAnalyticsDocuments(requestId: string): Promise<void> {
		const startTime = Date.now();

		try {
			// First, get the request details to check current status
			const request = await this.analyticsDocumentRequestRepository.findOne({
				where: { id: requestId }
			});

			if (!request) {
				throw new Error(`Analytics document request with ID ${requestId} not found`);
			}

			// Check if already processing or completed to prevent duplicates
			if (request.status === AnalyticsDocumentStatus.PROCESSING) {
				this.logger.warn(`Request ${requestId} is already being processed, skipping duplicate`);
				return;
			}

			if (request.status === AnalyticsDocumentStatus.COMPLETED) {
				this.logger.warn(`Request ${requestId} is already completed, skipping duplicate`);
				return;
			}

			// Atomically update status to PROCESSING to prevent race conditions
			const updateResult = await this.analyticsDocumentRequestRepository.update(
				{
					id: requestId,
					status: AnalyticsDocumentStatus.PENDING // Only update if still PENDING
				},
				{
					status: AnalyticsDocumentStatus.PROCESSING,
					updatedAt: new Date()
				}
			);

			// If no rows were affected, another process already started processing
			if (updateResult.affected === 0) {
				this.logger.warn(`Request ${requestId} status was already changed by another process, skipping duplicate`);
				return;
			}

			this.logger.log('🚀 Starting analytics document processing', {
				requestId
			});

			// Log processing start for debugging
			this.logger.debug('🚀 ANALYTICS PROCESSING STARTED', {
				requestId: requestId,
				startedAt: new Date().toISOString()
			});

			// Process documents by type (placeholder - will be implemented in Phase 2)
			const result = await this.processDocumentsByType(request);

			// Send email with documents (placeholder - will be implemented in Phase 2)
			await this.sendAnalyticsEmail(request, result);

			// Update status to completed
			await this.analyticsDocumentRequestRepository.update(
				{ id: requestId },
				{
					status: AnalyticsDocumentStatus.COMPLETED,
					processedAt: new Date(),
					documentCount: result.documentCount,
					totalSize: result.totalSize,
					pdfFileKey: result.pdfFileKey,
					excelFileKey: result.excelFileKey,
					updatedAt: new Date()
				}
			);

			// Record performance metrics
			const processingTimeMs = Date.now() - startTime;
			await this.analyticsMonitoringService.recordPerformanceMetrics(requestId, {
				processingTimeMs,
				documentCount: result.documentCount,
				totalSizeBytes: result.totalSize
			});

			this.logger.log('✅ Analytics document processing completed', {
				requestId,
				documentCount: result.documentCount,
				totalSize: result.totalSize,
				processingTimeMs
			});

			// Log completion details for debugging
			this.logger.debug('✅ ANALYTICS PROCESSING COMPLETED', {
				requestId: requestId,
				documentsProcessed: result.documentCount,
				totalSizeKB: Math.round(result.totalSize / 1024),
				processingTimeMs: processingTimeMs,
				completedAt: new Date().toISOString()
			});
		} catch (error) {
			this.logger.error('Analytics document processing failed', {
				requestId,
				error: error instanceof Error ? error.message : String(error)
			});

			// Update status to failed
			await this.analyticsDocumentRequestRepository.update(
				{ id: requestId },
				{
					status: AnalyticsDocumentStatus.FAILED,
					errorMessage: error instanceof Error ? error.message : String(error),
					updatedAt: new Date()
				}
			);

			// Don't re-throw to avoid infinite SQS retries
		}
	}

	/**
	 * Process documents by type - Generate both PDF and Excel files
	 */
	private async processDocumentsByType(
		request: AnalyticsDocumentRequestEntity
	): Promise<AnalyticsDocumentProcessingResult> {
		this.logger.log('Processing documents for analytics request', {
			requestId: request.id,
			documentType: request.documentType,
			startDate: request.startDate,
			endDate: request.endDate
		});

		// Ensure dates are proper Date objects
		const startDate = new Date(request.startDate);
		const endDate = new Date(request.endDate);

		// Validate period (max 1 month)
		const periodDays = Math.ceil(
			(endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
		);
		if (periodDays > 31) {
			throw new Error('Period cannot exceed 1 month (31 days)');
		}

		// Create request object with proper Date objects
		const requestWithDates = {
			...request,
			startDate,
			endDate
		};

		// Fetch data based on document type
		let documentData: any[] = [];
		let excelData: ExcelReportData = {};

		switch (request.documentType) {
			case AnalyticsDocumentType.INVOICE:
				documentData = await this.fetchInvoiceData(requestWithDates);
				excelData.invoices = await this.convertInvoicesToExcelFormat(documentData);
				break;
			case AnalyticsDocumentType.RECEIPT:
				documentData = await this.fetchReceiptData(requestWithDates);
				excelData.receipts = this.convertReceiptsToExcelFormat(documentData);
				break;
			case AnalyticsDocumentType.CREDIT_NOTE:
				documentData = await this.fetchCreditNoteData(requestWithDates);
				excelData.creditNotes = this.convertCreditNotesToExcelFormat(documentData);
				break;
			default:
				throw new Error(`Unsupported document type: ${request.documentType}`);
		}

		// Enforce document limit (max 5000)
		if (documentData.length > 5000) {
			throw new Error(`Too many documents found (${documentData.length}). Maximum allowed is 5000.`);
		}

		// Generate Excel buffer
		const excelBuffer = this.generateExcelReport(excelData, request.documentType);

		// Log document data before PDF generation
		this.logger.log('📄 Document data summary before PDF generation', {
			requestId: request.id,
			documentType: request.documentType,
			documentCount: documentData.length,
			sampleDocument: documentData.length > 0 ? {
				id: documentData[0].id,
				createdAt: documentData[0].createdAt,
				amount: documentData[0].invoiceAmount || documentData[0].amount || 'N/A'
			} : null
		});

		// Generate PDF buffer with actual document stitching
		const pdfBuffer = await this.generatePdfReport(documentData, request);

		const totalSize = excelBuffer.length + pdfBuffer.length;

		// Upload files to S3
		const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
		const pdfFileKey = `analytics-documents/${request.clinicId}/${request.documentType.toLowerCase()}-${timestamp}.pdf`;
		const excelFileKey = `analytics-documents/${request.clinicId}/${request.documentType.toLowerCase()}-${timestamp}.xlsx`;

		try {
			// Upload PDF to S3
			await this.s3Service.uploadPdfToS3(
				pdfBuffer,
				pdfFileKey
			);

			// Upload Excel to S3
			await this.uploadExcelToS3(
				excelBuffer,
				excelFileKey
			);

			this.logger.log('Files uploaded to S3', {
				requestId: request.id,
				pdfFileKey,
				excelFileKey
			});
		} catch (error) {
			this.logger.error('Failed to upload files to S3', {
				requestId: request.id,
				error: error instanceof Error ? error.message : String(error)
			});
			throw error;
		}

		this.logger.log('Document processing completed', {
			requestId: request.id,
			documentCount: documentData.length,
			totalSize,
			excelSize: excelBuffer.length,
			pdfSize: pdfBuffer.length
		});

		return {
			pdfBuffer,
			excelBuffer,
			documentCount: documentData.length,
			totalSize,
			pdfFileKey,
			excelFileKey
		};
	}

	/**
	 * Fetch invoice data for the specified period
	 */
	private async fetchInvoiceData(request: AnalyticsDocumentRequestEntity): Promise<any[]> {
		this.logger.log('DEBUG: Starting invoice fetch with parameters', {
			requestId: request.id,
			clinicId: request.clinicId,
			brandId: request.brandId,
			startDate: request.startDate,
			endDate: request.endDate
		});

		// First, let's check ALL invoices for this clinic (no filters)
		const allInvoicesForClinic = await this.invoiceRepository
			.createQueryBuilder('invoice')
			.where('invoice.clinicId = :clinicId', { clinicId: request.clinicId })
			.getMany();

		this.logger.log('DEBUG: All invoices for clinic', {
			requestId: request.id,
			clinicId: request.clinicId,
			totalInvoicesForClinic: allInvoicesForClinic.length
		});

		// Now check invoices for clinic + brand
		const invoicesForBrand = await this.invoiceRepository
			.createQueryBuilder('invoice')
			.where('invoice.clinicId = :clinicId', { clinicId: request.clinicId })
			.andWhere('invoice.brandId = :brandId', { brandId: request.brandId })
			.getMany();

		this.logger.log('DEBUG: Invoices for clinic + brand', {
			requestId: request.id,
			clinicId: request.clinicId,
			brandId: request.brandId,
			invoicesForBrand: invoicesForBrand.length
		});

		// Now add date filter
		const invoicesInDateRange = await this.invoiceRepository
			.createQueryBuilder('invoice')
			.where('invoice.clinicId = :clinicId', { clinicId: request.clinicId })
			.andWhere('invoice.brandId = :brandId', { brandId: request.brandId })
			.andWhere('invoice.createdAt >= :startDate', { startDate: request.startDate })
			.andWhere('invoice.createdAt <= :endDate', { endDate: request.endDate })
			.getMany();

		this.logger.log('DEBUG: Invoices in date range', {
			requestId: request.id,
			startDate: request.startDate,
			endDate: request.endDate,
			invoicesInDateRange: invoicesInDateRange.length
		});

		// Finally, add invoice type filter (Invoice entity doesn't have direct patient/owner relations)
		const invoices = await this.invoiceRepository
			.createQueryBuilder('invoice')
			.where('invoice.clinicId = :clinicId', { clinicId: request.clinicId })
			.andWhere('invoice.brandId = :brandId', { brandId: request.brandId })
			.andWhere('invoice.createdAt >= :startDate', { startDate: request.startDate })
			.andWhere('invoice.createdAt <= :endDate', { endDate: request.endDate })
			.andWhere('invoice.invoiceType = :invoiceType', { invoiceType: EnumInvoiceType.Invoice })
			.orderBy('invoice.createdAt', 'DESC')
			.getMany();

		this.logger.log('Fetched invoice data', {
			requestId: request.id,
			count: invoices.length
		});

		// Fetch related patient and owner data in batches to avoid N+1 queries
		if (invoices.length > 0) {
			const patientIds = [...new Set(invoices.map(inv => inv.patientId))];
			const ownerIds = [...new Set(invoices.map(inv => inv.ownerId))];

			// Batch fetch patients using modern approach
			const patients = await this.patientRepository.find({
				where: { id: In(patientIds) }
			});
			const patientMap = new Map(patients.map(p => [p.id, p]));

			// Batch fetch owners using modern approach
			const owners = await this.ownerBrandRepository.find({
				where: { id: In(ownerIds) }
			});
			const ownerMap = new Map(owners.map(o => [o.id, o]));

			// Attach the related data to invoices
			invoices.forEach(invoice => {
				(invoice as any).patient = patientMap.get(invoice.patientId);
				(invoice as any).ownerBrand = ownerMap.get(invoice.ownerId);
			});
		}

		return invoices;
	}

	/**
	 * Fetch receipt data for the specified period
	 */
	private async fetchReceiptData(request: AnalyticsDocumentRequestEntity): Promise<any[]> {
		const receipts = await this.paymentDetailsRepository
			.createQueryBuilder('payment')
			.leftJoinAndSelect('payment.patient', 'patient')
			.leftJoinAndSelect('payment.ownerBrand', 'ownerBrand')
			.where('payment.clinicId = :clinicId', { clinicId: request.clinicId })
			.andWhere('payment.brandId = :brandId', { brandId: request.brandId })
			.andWhere('payment.createdAt >= :startDate', { startDate: request.startDate })
			.andWhere('payment.createdAt <= :endDate', { endDate: request.endDate })
			.andWhere('payment.type IN (:...types)', {
				types: [EnumAmountType.Collect, EnumAmountType.Return]
			})
			.orderBy('payment.createdAt', 'DESC')
			.getMany();

		this.logger.log('Fetched receipt data', {
			requestId: request.id,
			count: receipts.length
		});

		return receipts;
	}

	/**
	 * Fetch credit note data for the specified period
	 */
	private async fetchCreditNoteData(request: AnalyticsDocumentRequestEntity): Promise<any[]> {
		// First, let's check what credit note data exists
		const allCreditNotes = await this.paymentDetailsRepository
			.createQueryBuilder('payment')
			.where('payment.clinicId = :clinicId', { clinicId: request.clinicId })
			.andWhere('payment.brandId = :brandId', { brandId: request.brandId })
			.andWhere('payment.type = :type', { type: EnumAmountType.CreditNote })
			.getMany();

		this.logger.log('All credit notes for clinic', {
			requestId: request.id,
			clinicId: request.clinicId,
			brandId: request.brandId,
			totalCreditNotes: allCreditNotes.length,
			dateRange: `${request.startDate} to ${request.endDate}`
		});

		// Now get credit notes for the specific date range
		const creditNotes = await this.paymentDetailsRepository
			.createQueryBuilder('payment')
			.where('payment.clinicId = :clinicId', { clinicId: request.clinicId })
			.andWhere('payment.brandId = :brandId', { brandId: request.brandId })
			.andWhere('payment.createdAt >= :startDate', { startDate: request.startDate })
			.andWhere('payment.createdAt <= :endDate', { endDate: request.endDate })
			.andWhere('payment.type = :type', { type: EnumAmountType.CreditNote })
			.orderBy('payment.createdAt', 'DESC')
			.getMany();

		this.logger.log('Fetched credit note data for date range', {
			requestId: request.id,
			count: creditNotes.length,
			startDate: request.startDate,
			endDate: request.endDate
		});

		// Fetch related patient and owner data in batches to avoid N+1 queries
		if (creditNotes.length > 0) {
			const patientIds = [...new Set(creditNotes.filter(cn => cn.patientId).map(cn => cn.patientId))];
			const ownerIds = [...new Set(creditNotes.map(cn => cn.ownerId))];

			// Batch fetch patients using modern approach
			const patients = patientIds.length > 0 ? await this.patientRepository.find({
				where: { id: In(patientIds) }
			}) : [];
			const patientMap = new Map(patients.map(p => [p.id, p]));

			// Batch fetch owners using modern approach
			const owners = await this.ownerBrandRepository.find({
				where: { id: In(ownerIds) }
			});
			const ownerMap = new Map(owners.map(o => [o.id, o]));

			// Attach the related data to credit notes
			creditNotes.forEach(creditNote => {
				(creditNote as any).patient = creditNote.patientId ? patientMap.get(creditNote.patientId) : null;
				(creditNote as any).ownerBrand = ownerMap.get(creditNote.ownerId);
			});
		}

		return creditNotes;
	}

	/**
	 * Convert invoice data to Excel format
	 */
	private async convertInvoicesToExcelFormat(invoices: any[]): Promise<InvoiceExcelRow[]> {
		const excelRows: InvoiceExcelRow[] = [];

		for (const invoice of invoices) {
			// Use preloaded data from eager loading instead of additional queries
			const ownerName = invoice.ownerBrand
				? `${invoice.ownerBrand.firstName || ''} ${invoice.ownerBrand.lastName || ''}`.trim()
				: 'N/A';

			const petName = invoice.patient?.patientName || 'N/A';

			excelRows.push({
				date: invoice.createdAt.toLocaleDateString('en-GB'),
				client: ownerName,
				pet: petName,
				invoiceNumber: invoice.referenceAlphaId || `#${invoice.referenceId}`,
				invoiceStatus: invoice.status || 'Unknown',
				invoiceAmount: Number(invoice.invoiceAmount) || 0,
				invoiceBalance: Number(invoice.balanceDue) || 0
			});
		}

		return excelRows;
	}

	/**
	 * Upload Excel file to S3 with proper Promise handling
	 */
	private async uploadExcelToS3(
		excelBuffer: Buffer,
		fileKey: string
	): Promise<any> {
		return new Promise((resolve, reject) => {
			const params = {
				Bucket: this.s3Service['bucketName'], // Access private property
				Key: fileKey,
				Body: excelBuffer,
				ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			};

			this.s3Service['s3Client'].putObject(params, (err: any, data: any) => {
				if (err) {
					this.logger.error('S3 Excel upload error', {
						fileKey,
						error: err.message || err
					});
					reject(err);
				} else {
					this.logger.log('Excel file uploaded to S3 successfully', {
						fileKey
					});
					resolve(data);
				}
			});
		});
	}

	/**
	 * Convert receipt data to Excel format
	 */
	private convertReceiptsToExcelFormat(receipts: any[]): ReceiptExcelRow[] {
		return receipts.map(receipt => {
			const ownerName = receipt.ownerBrand
				? `${receipt.ownerBrand.firstName || ''} ${receipt.ownerBrand.lastName || ''}`.trim()
				: 'N/A';

			const transactionType = receipt.type === EnumAmountType.Collect ? 'Collected' : 'Returned';

			return {
				date: receipt.createdAt.toLocaleDateString('en-GB'),
				client: ownerName,
				receiptNumber: receipt.referenceAlphaId || `#${receipt.referenceId}`,
				amount: Number(receipt.amount) || 0,
				transaction: transactionType,
				paymentMode: receipt.paymentType || 'Cash'
			};
		});
	}

	/**
	 * Convert credit note data to Excel format
	 */
	private convertCreditNotesToExcelFormat(creditNotes: any[]): CreditNoteExcelRow[] {
		return creditNotes.map(creditNote => {
			const ownerName = creditNote.ownerBrand
				? `${creditNote.ownerBrand.firstName || ''} ${creditNote.ownerBrand.lastName || ''}`.trim()
				: 'N/A';

			const referenceInvoice = creditNote.invoice?.referenceAlphaId ||
				(creditNote.invoice?.referenceId ? `#${creditNote.invoice.referenceId}` : 'N/A');

			return {
				date: creditNote.createdAt.toLocaleDateString('en-GB'),
				client: ownerName,
				creditNoteNumber: creditNote.referenceAlphaId || `#${creditNote.referenceId}`,
				referenceInvoice,
				amountReturned: Number(creditNote.amount) || 0
			};
		});
	}

	/**
	 * Generate Excel report from data
	 */
	private generateExcelReport(data: ExcelReportData, documentType: AnalyticsDocumentType): Buffer {
		const workbook = XLSX.utils.book_new();

		switch (documentType) {
			case AnalyticsDocumentType.INVOICE:
				if (data.invoices && data.invoices.length > 0) {
					const worksheet = XLSX.utils.json_to_sheet(data.invoices);

					// Set column widths for better formatting
					worksheet['!cols'] = [
						{ width: 12 }, // Date
						{ width: 20 }, // Client
						{ width: 15 }, // Pet
						{ width: 15 }, // Invoice Number
						{ width: 15 }, // Invoice Status
						{ width: 15 }, // Invoice Amount
						{ width: 15 }  // Invoice Balance
					];

					XLSX.utils.book_append_sheet(workbook, worksheet, 'Invoices');
				} else {
					// Create empty sheet with headers
					const emptyData = [{
						date: '',
						client: '',
						pet: '',
						invoiceNumber: '',
						invoiceStatus: '',
						invoiceAmount: '',
						invoiceBalance: ''
					}];
					const worksheet = XLSX.utils.json_to_sheet(emptyData);
					XLSX.utils.book_append_sheet(workbook, worksheet, 'Invoices');
				}
				break;

			case AnalyticsDocumentType.RECEIPT:
				if (data.receipts && data.receipts.length > 0) {
					const worksheet = XLSX.utils.json_to_sheet(data.receipts);

					worksheet['!cols'] = [
						{ width: 12 }, // Date
						{ width: 20 }, // Client
						{ width: 15 }, // Receipt Number
						{ width: 15 }, // Amount
						{ width: 15 }, // Transaction
						{ width: 15 }  // Payment Mode
					];

					XLSX.utils.book_append_sheet(workbook, worksheet, 'Receipts');
				} else {
					const emptyData = [{
						date: '',
						client: '',
						receiptNumber: '',
						amount: '',
						transaction: '',
						paymentMode: ''
					}];
					const worksheet = XLSX.utils.json_to_sheet(emptyData);
					XLSX.utils.book_append_sheet(workbook, worksheet, 'Receipts');
				}
				break;

			case AnalyticsDocumentType.CREDIT_NOTE:
				if (data.creditNotes && data.creditNotes.length > 0) {
					const worksheet = XLSX.utils.json_to_sheet(data.creditNotes);

					worksheet['!cols'] = [
						{ width: 12 }, // Date
						{ width: 20 }, // Client
						{ width: 18 }, // Credit Note Number
						{ width: 18 }, // Reference Invoice
						{ width: 15 }  // Amount Returned
					];

					XLSX.utils.book_append_sheet(workbook, worksheet, 'Credit Notes');
				} else {
					const emptyData = [{
						date: '',
						client: '',
						creditNoteNumber: '',
						referenceInvoice: '',
						amountReturned: ''
					}];
					const worksheet = XLSX.utils.json_to_sheet(emptyData);
					XLSX.utils.book_append_sheet(workbook, worksheet, 'Credit Notes');
				}
				break;
		}

		// Convert workbook to buffer
		const excelBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

		this.logger.log('Excel report generated', {
			documentType,
			bufferSize: excelBuffer.length
		});

		return excelBuffer;
	}

	/**
	 * Generate PDF report by stitching individual document PDFs
	 */
	private async generatePdfReport(
		documentData: any[],
		request: AnalyticsDocumentRequestEntity
	): Promise<Buffer> {
		// Log PDF generation debug information
		this.logger.debug('📄 PDF GENERATION DEBUG', {
			requestId: request.id,
			documentType: request.documentType,
			documentCount: documentData.length,
			dateRange: `${new Date(request.startDate).toDateString()} to ${new Date(request.endDate).toDateString()}`
		});

		if (documentData.length === 0) {
			this.logger.debug('⚠️  No documents found - generating "No Data" PDF');
			// Return a simple "No documents found" PDF
			const noDataHtml = `
				<!DOCTYPE html>
				<html>
				<head>
					<style>
						body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
						h1 { color: #666; }
					</style>
				</head>
				<body>
					<h1>No ${request.documentType.toLowerCase()}s found</h1>
					<p>No documents were found for the specified date range.</p>
					<p>Period: ${new Date(request.startDate).toDateString()} to ${new Date(request.endDate).toDateString()}</p>
				</body>
				</html>
			`;
			const buffer = await generatePDFBuffer(noDataHtml);
			this.logger.debug('✅ "No Data" PDF generated successfully', {
				bufferSize: buffer.length
			});
			return buffer;
		}

		this.logger.log(`Processing ${documentData.length} documents for PDF generation`, {
			requestId: request.id,
			documentType: request.documentType,
			documentCount: documentData.length
		});

		const htmlChunks: string[] = [];

		for (const document of documentData) {
			try {
				const html = await this._generateDocumentHtml(document, request.documentType);
				htmlChunks.push(html);
			} catch (error) {
				this.logger.error('Error generating HTML for document', {
					documentId: document.id,
					error: error instanceof Error ? error.message : String(error)
				});
				// Continue with other documents instead of failing completely
			}
		}

		if (htmlChunks.length === 0) {
			this.logger.warn('No HTML chunks were generated successfully', {
				requestId: request.id,
				documentCount: documentData.length,
				documentType: request.documentType
			});

			const errorHtml = `
				<!DOCTYPE html>
				<html>
				<head>
					<style>
						body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
						h1 { color: #d32f2f; }
					</style>
				</head>
				<body>
					<h1>Error generating documents</h1>
					<p>Unable to generate PDF documents for the specified criteria.</p>
					<p>Found ${documentData.length} documents but failed to generate PDFs for all of them.</p>
				</body>
				</html>
			`;
			return await generatePDFBuffer(errorHtml);
		}

		const combinedHtml = htmlChunks.join('<div style="page-break-before: always;"></div>');

		const finalHtml = `
		    <!DOCTYPE html>
		    <html>
		      <head>
		        <meta charset="utf-8" />
		      </head>
		      <body>
		        ${combinedHtml}
		      </body>
		    </html>
		  `;

		const pdfBuffer = await generatePDFBuffer(finalHtml);

		this.logger.log('PDF report generated successfully from combined HTML', {
			documentType: request.documentType,
			documentCount: documentData.length,
			successfulHtmls: htmlChunks.length,
			bufferSize: pdfBuffer.length
		});

		return pdfBuffer;
	}

	/**
		* Generate HTML for a single document without creating a PDF
		*/
	private async _generateDocumentHtml(
		document: any,
		documentType: AnalyticsDocumentType
	): Promise<string> {
		switch (documentType) {
			case AnalyticsDocumentType.INVOICE: {
				const invoiceData = await this.prepareInvoiceData(
					document,
					document.patient,
					document.ownerBrand
				);
				return generateNewInvoice(invoiceData);
			}
			case AnalyticsDocumentType.RECEIPT: {
				const receiptData = await this.prepareReceiptData(document);
				return generateNewPaymentReceipt(receiptData);
			}
			case AnalyticsDocumentType.CREDIT_NOTE: {
				const creditNoteData = await this.prepareCreditNoteData(
					document,
					document.patient,
					document.ownerBrand
				);
				return generateNewCreditNote(creditNoteData);
			}
			default:
				throw new Error(`Unsupported document type for HTML generation: ${documentType}`);
		}
	}

	/**
		* Prepare invoice data for professional template
	 * Uses preloaded data to avoid N+1 queries
	 */
	private async prepareInvoiceData(invoice: any, patientDetails?: any, ownerDetails?: any): Promise<InvoiceData> {
		// If patient details not provided, fetch them (fallback for backward compatibility)
		let patient = patientDetails;
		if (!patient) {
			patient = await this.patientRepository.findOne({
				where: { id: invoice.patientId },
				relations: [
					'clinic',
					'clinic.brand',
					'patientOwners',
					'patientOwners.ownerBrand',
					'patientOwners.ownerBrand.globalOwner'
				]
			});
		}

		if (!patient) {
			throw new Error(`Patient details not found for invoice ${invoice.id}`);
		}

		// Get clinic details from patient or fetch separately
		let clinicDetails = patient.clinic;
		if (!clinicDetails) {
			clinicDetails = await this.clinicRepository.findOne({
				where: { id: invoice.clinicId },
				relations: ['brand']
			});
		}

		if (!clinicDetails) {
			throw new Error(`Clinic details not found for invoice ${invoice.id}`);
		}

		// Use provided owner details or get from patient relations
		let owner = ownerDetails;
		if (!owner) {
			const patientOwner = patient.patientOwners?.[0];
			if (!patientOwner?.ownerBrand) {
				throw new Error(`Owner details not found for invoice ${invoice.id}`);
			}
			owner = patientOwner.ownerBrand;
		}

		// Handle clinic logo URL (same pattern as send-document service)
		let clinicLogoUrl = clinicDetails?.logoUrl || clinicDetails?.clinicLogo || '';

		// Get pre-signed URL for clinic logo if it exists and is an S3 path
		if (clinicLogoUrl && clinicLogoUrl.startsWith('clinicLogo/')) {
			try {
				const logoPreSignedUrl = await this.s3Service.getViewPreSignedUrl(clinicLogoUrl);
				clinicLogoUrl = logoPreSignedUrl;
			} catch (error) {
				this.logger.warn('Could not get clinic logo URL', {
					logoUrl: clinicLogoUrl,
					error: error instanceof Error ? error.message : String(error)
				});
				clinicLogoUrl = ''; // Set to empty if there's an error
			}
		}

		// Extract line items from invoice details
		const lineItems = (invoice.details || []).map((item: any) => ({
			description: item.name || 'Service',
			quantity: item.quantity || 1,
			price: Number(item.actualPrice) || 0
		}));

		// Prepare invoice data using the same structure as send-document service
		const invoiceData: InvoiceData = {
			invoiceNumber: invoice.referenceAlphaId || '',
			invoiceDate: moment(invoice.createdAt).format('MMMM D, YYYY'),
			clinicName: clinicDetails?.name || '',
			clinicAddress: this.getClinicAddress(clinicDetails),
			clinicPhone: clinicDetails?.phoneNumbers?.[0]?.number || clinicDetails?.mobile || '',
			clinicEmail: clinicDetails?.email || '',
			clinicWebsite: clinicDetails?.website || '',
			customerName: owner ? `${owner.firstName || ''} ${owner.lastName || ''}`.trim() : 'N/A',
			petName: patient.patientName || '',
			petDetails: `${patient.species || ''} ${patient.breed || ''}`.trim() || 'Pet',
			customerEmail: owner?.email || '',
			customerPhone: owner?.globalOwner
				? `${owner.globalOwner.countryCode || ''}${owner.globalOwner.phoneNumber || ''}`
				: '',
			clinicLogoUrl,
			// Invoice line items
			lineItems,
			// Invoice totals
			subtotal: Number(invoice.totalPrice) || 0,
			taxes: Number(invoice.totalTax) || 0,
			discount: Number(invoice.totalDiscount) || 0,
			previousBalance: Number(invoice.totalCredit) || 0,
			invoiceAmount: Number(invoice.invoiceAmount) || 0,
			totalDue: Number(invoice.invoiceAmount) || 0,
			amountPaid: Number(invoice.amountPaid) || 0,
			balanceDue: Number(invoice.balanceDue) || 0,
			// Optional fields - set to defaults for analytics
			receiptDate: '',
			paymentMode: '',
			receiptNumber: '',
			creditsUsed: 0,
			refunds: 0,
			refundCreditNote: '',
			refundAmount: 0,
			refundDate: '',
			refundItems: [],
			paymentItems: []
		};

		return invoiceData;
	}

	/**
	 * Prepare receipt data for professional template
	 */
	private async prepareReceiptData(receipt: any): Promise<ReceiptData> {
		// For receipts, get the full payment details with owner and clinic info
		// This matches the pattern used in send-document service
		const paymentDetails = await this.paymentDetailsRepository.findOne({
			where: { id: receipt.id },
			relations: [
				'ownerBrand',
				'patient',
				'clinic',
				'clinic.brand'
			]
		});

		if (!paymentDetails) {
			throw new Error(`Payment details not found for receipt ${receipt.id}`);
		}

		const clinic = paymentDetails.clinic;
		const ownerBrand = paymentDetails.ownerBrand;

		// Get customer name from ownerBrand (same pattern as send-document service)
		const customerName = ownerBrand
			? `${ownerBrand.firstName || ''} ${ownerBrand.lastName || ''}`.trim()
			: 'N/A';

		// Handle clinic logo URL (same pattern as send-document service)
		let clinicLogoUrl = clinic?.logoUrl || clinic?.clinicLogo || '';

		if (clinicLogoUrl && clinicLogoUrl.startsWith('clinicLogo/')) {
			try {
				const logoPreSignedUrl = await this.s3Service.getViewPreSignedUrl(clinicLogoUrl);
				clinicLogoUrl = logoPreSignedUrl;
			} catch (error) {
				this.logger.warn('Could not get clinic logo URL for receipt', {
					logoUrl: clinicLogoUrl,
					error: error instanceof Error ? error.message : String(error)
				});
				clinicLogoUrl = '';
			}
		}

		// Prepare receipt data using the same structure as send-document service
		const receiptData: ReceiptData = {
			receiptNumber: receipt.referenceAlphaId || '',
			receiptDate: moment(receipt.createdAt).format('Do MMM YYYY'),
			clinicName: clinic?.name || '',
			clinicAddress: this.getClinicAddress(clinic),
			clinicPhone: clinic?.phoneNumbers?.[0]?.number || clinic?.mobile || '',
			clinicEmail: clinic?.email || '',
			clinicWebsite: clinic?.website || '',
			customerName,
			amount: Number(receipt.amount) || 0,
			paymentType: receipt.paymentType || 'Cash',
			clinicLogoUrl,
			creditsAdded: Number(receipt.creditAmountAdded) || 0,
			outstandingInvoicesPaid: [] // For analytics, we'll keep this empty for simplicity
		};

		return receiptData;
	}

	/**
	 * Prepare credit note data for professional template
	 * Uses preloaded data to avoid N+1 queries
	 */
	private async prepareCreditNoteData(creditNote: any, patientDetails?: any, ownerDetails?: any): Promise<CreditNoteData> {
		// If patient details not provided, fetch them (fallback for backward compatibility)
		let patient = patientDetails;
		if (!patient) {
			patient = await this.patientRepository.findOne({
				where: { id: creditNote.patientId },
				relations: [
					'clinic',
					'clinic.brand',
					'patientOwners',
					'patientOwners.ownerBrand',
					'patientOwners.ownerBrand.globalOwner'
				]
			});
		}

		if (!patient) {
			throw new Error(`Patient details not found for credit note ${creditNote.id}`);
		}

		// Get clinic details from patient or fetch separately
		let clinicDetails = patient.clinic;
		if (!clinicDetails) {
			clinicDetails = await this.clinicRepository.findOne({
				where: { id: creditNote.clinicId },
				relations: ['brand']
			});
		}

		if (!clinicDetails) {
			throw new Error(`Clinic details not found for credit note ${creditNote.id}`);
		}

		// Use provided owner details or get from patient relations
		let owner = ownerDetails;
		if (!owner) {
			const patientOwner = patient.patientOwners?.[0];
			if (!patientOwner?.ownerBrand) {
				throw new Error(`Owner details not found for credit note ${creditNote.id}`);
			}
			owner = patientOwner.ownerBrand;
		}

		// Get customer name (same pattern as send-document service)
		const customerName = `${owner.firstName || ''} ${owner.lastName || ''}`.trim();

		// Get patient info
		const petName = patient.patientName || 'N/A';
		const petDetails = `${patient.species || ''} ${patient.breed || ''}`.trim() || 'Pet';

		// Handle clinic logo URL (same pattern as send-document service)
		let clinicLogoUrl = clinicDetails?.logoUrl || clinicDetails?.clinicLogo || '';

		if (clinicLogoUrl && clinicLogoUrl.startsWith('clinicLogo/')) {
			try {
				const logoPreSignedUrl = await this.s3Service.getViewPreSignedUrl(clinicLogoUrl);
				clinicLogoUrl = logoPreSignedUrl;
			} catch (error) {
				this.logger.warn('Could not get clinic logo URL for credit note', {
					logoUrl: clinicLogoUrl,
					error: error instanceof Error ? error.message : String(error)
				});
				clinicLogoUrl = '';
			}
		}

		// Get payment details for this credit note (same pattern as send-document service)
		const paymentDetails = await this.paymentDetailsRepository.find({
			where: { invoiceId: creditNote.id },
			order: { createdAt: 'ASC' }
		});

		// Get original invoice if this is a refund
		let originalInvoice = null;
		if (creditNote.invoiceType === EnumInvoiceType.Refund) {
			originalInvoice = await this.invoiceRepository.findOne({
				where: {
					cartId: creditNote.cartId,
					invoiceType: EnumInvoiceType.Invoice
				}
			});
		}

		// Find specific payment details (same pattern as send-document service)
		const creditNotePayment = paymentDetails.find(
			payment => payment.type === EnumAmountType.CreditNote
		);
		const collectPayment = paymentDetails.find(
			payment => payment.type === EnumAmountType.Collect && payment.isCreditsAdded
		);

		// Extract line items from credit note details
		const lineItems = (creditNote.details || []).map((item: any) => ({
			description: item.name || 'Service',
			quantity: item.quantity || 1,
			price: Number(item.actualPrice) || 0
		}));

		// Prepare credit note data using the same structure as send-document service
		const creditNoteData: CreditNoteData = {
			creditNoteNumber: creditNote.referenceAlphaId || '',
			creditNoteDate: moment(creditNote.createdAt).format('Do MMM YYYY'),
			clinicName: clinicDetails?.name || '',
			clinicAddress: this.getClinicAddress(clinicDetails),
			clinicPhone: clinicDetails?.phoneNumbers?.[0]?.number || clinicDetails?.mobile || '',
			clinicEmail: clinicDetails?.email || '',
			clinicWebsite: clinicDetails?.website || '',
			customerName,
			petName,
			petDetails,
			lineItems,
			adjustments: Number(creditNote.totalTax) + Number(creditNote.totalDiscount) || 0,
			totalDue: Number(creditNote.amountPayable) || 0,
			amountPaid: creditNotePayment ? Number(creditNotePayment.amount) || 0 : 0,
			balanceDue: Number(creditNote.balanceDue) || 0,
			invoiceDate: originalInvoice
				? moment(originalInvoice.createdAt).format('MMMM D, YYYY')
				: '',
			invoiceId: originalInvoice
				? originalInvoice.referenceAlphaId || ''
				: '',
			clinicLogoUrl,
			referenceInvoice: originalInvoice
				? originalInvoice.referenceAlphaId || ''
				: '',
			// Optional fields (same pattern as send-document service)
			refundAmount: Number(creditNote.invoiceAmount) || 0,
			receiptDate: creditNotePayment
				? moment(creditNotePayment.createdAt).format('Do MMM YYYY')
				: moment(creditNote.createdAt).format('Do MMM YYYY'),
			paymentMode: creditNotePayment
				? creditNotePayment.paymentType || ''
				: creditNote.paymentMode || '',
			receiptNumber: creditNotePayment
				? creditNotePayment.referenceAlphaId || ''
				: '',
			receiptNumberCredits: collectPayment
				? collectPayment.referenceAlphaId || ''
				: '',
			creditsAdded: collectPayment
				? Number(collectPayment.creditAmountAdded) || 0
				: 0
		};

		return creditNoteData;
	}

	/**
	 * Get formatted clinic address (same pattern as send-document service)
	 */
	private getClinicAddress(clinic: any): string {
		if (!clinic) return '';

		const addressParts = [
			clinic.addressLine1,
			clinic.addressLine2,
			clinic.city,
			clinic.state,
			clinic.addressPincode
		].filter(Boolean);

		return addressParts.join(', ');
	}

	/**
	 * Send analytics email with PDF and Excel attachments
	 */
	private async sendAnalyticsEmail(
		request: AnalyticsDocumentRequestEntity,
		result: AnalyticsDocumentProcessingResult
	): Promise<void> {
		try {
			// Validate recipient email
			if (!request.recipientEmail) {
				throw new Error('Recipient email is required for sending analytics report');
			}

			// Create email content
			const emailSubject = `Analytics Report - ${request.documentType} (${new Date(request.startDate).toDateString()} to ${new Date(request.endDate).toDateString()})`;

			const emailBody = `
				<p>Hey,</p>
				<br>
				<p><strong><em>Attached are the requested document and its accompanying report.</em></strong></p>
				<br>

				<p>Regards,</p>
				<br>
				<p>Nidana</p>
			`;

			// Prepare attachments
			const buffers = [result.pdfBuffer, result.excelBuffer];
			const fileNames = [
				`${request.documentType.toLowerCase()}-report-${new Date().toISOString().split('T')[0]}.pdf`,
				`${request.documentType.toLowerCase()}-report-${new Date().toISOString().split('T')[0]}.xlsx`
			];

			// Send emails to both recipients with error handling
			const bccEmail = ANALYTICS_BCC_EMAIL;
			const emailResults = {
				recipientSuccess: false,
				sachinSuccess: false,
				errors: [] as string[]
			};

			// Send email to the requested recipient
			try {
				await this.sendMail(
					emailBody,
					buffers,
					fileNames,
					request.recipientEmail,
					emailSubject
				);
				emailResults.recipientSuccess = true;
			} catch (error) {
				const errorMsg = `Failed to send email to ${request.recipientEmail}: ${error instanceof Error ? error.message : String(error)}`;
				emailResults.errors.push(errorMsg);
				this.logger.error('Failed to send analytics email to recipient', {
					requestId: request.id,
					recipientEmail: request.recipientEmail,
					error: errorMsg
				});
			}

			// Always send a copy to the configured BCC email
			try {
				await this.sendMail(
					emailBody,
					buffers,
					fileNames,
					bccEmail,
					emailSubject
				);
				emailResults.sachinSuccess = true;
			} catch (error) {
				const errorMsg = `Failed to send email to ${bccEmail}: ${error instanceof Error ? error.message : String(error)}`;
				emailResults.errors.push(errorMsg);
				this.logger.error('Failed to send analytics email to BCC recipient', {
					requestId: request.id,
					bccEmail: bccEmail,
					error: errorMsg
				});
			}

			// Check if at least one email was sent successfully
			if (!emailResults.recipientSuccess && !emailResults.sachinSuccess) {
				throw new Error(`Failed to send analytics email to both recipients: ${emailResults.errors.join('; ')}`);
			}

			this.logger.log('📧 Analytics Email sending completed:', {
				requestId: request.id,
				recipientEmail: request.recipientEmail,
				recipientSuccess: emailResults.recipientSuccess,
				bccEmail: bccEmail,
				sachinSuccess: emailResults.sachinSuccess,
				documentCount: result.documentCount,
				subject: emailSubject,
				attachments: fileNames,
				pdfSize: result.pdfBuffer.length,
				excelSize: result.excelBuffer.length,
				errors: emailResults.errors
			});

			// Log email completion summary for debugging
			this.logger.debug('📧 EMAIL SENDING COMPLETED', {
				to: request.recipientEmail,
				recipientSuccess: emailResults.recipientSuccess,
				copyTo: bccEmail,
				sachinSuccess: emailResults.sachinSuccess,
				subject: emailSubject,
				attachments: fileNames,
				pdfSizeKB: Math.round(result.pdfBuffer.length / 1024),
				excelSizeKB: Math.round(result.excelBuffer.length / 1024),
				errors: emailResults.errors.length > 0 ? emailResults.errors : undefined
			});

		} catch (error) {
			this.logger.error('Error in analytics email sending process', {
				requestId: request.id,
				recipientEmail: request.recipientEmail,
				bccEmail: ANALYTICS_BCC_EMAIL,
				error: error instanceof Error ? error.message : String(error)
			});
			throw error;
		}
	}

	/**
	 * Cleanup expired analytics document requests and their associated S3 files
	 * This method is called by the cron job daily
	 */
	async cleanupExpiredAnalyticsDocuments(): Promise<{
		deletedRequests: number;
		deletedFiles: number;
		errors: string[];
	}> {
		const result: {
			deletedRequests: number;
			deletedFiles: number;
			errors: string[];
		} = {
			deletedRequests: 0,
			deletedFiles: 0,
			errors: []
		};

		try {
			this.logger.log('=== 🧹 ANALYTICS DOCUMENT CLEANUP STARTED ===');

			// Find expired requests
			const expiredRequests = await this.analyticsDocumentRequestRepository.find({
				where: {
					expiresAt: LessThan(new Date())
				},
				select: ['id', 'pdfFileKey', 'excelFileKey', 'clinicId', 'documentType', 'expiresAt']
			});

			this.logger.log(`📋 Found ${expiredRequests.length} expired analytics document requests`);

			if (expiredRequests.length === 0) {
				this.logger.log('✅ No expired analytics documents to cleanup');
				return result;
			}

			// Process each expired request
			for (const request of expiredRequests) {
				try {
					// Delete S3 files if they exist
					const filesToDelete = [request.pdfFileKey, request.excelFileKey].filter((key): key is string => Boolean(key));

					for (const fileKey of filesToDelete) {
						try {
							await this.s3Service.deleteFile(fileKey);
							result.deletedFiles++;
							this.logger.log(`🗑️ Deleted S3 file: ${fileKey}`);
						} catch (fileError) {
							const errorMsg = `Failed to delete S3 file ${fileKey}: ${fileError instanceof Error ? fileError.message : String(fileError)}`;
							result.errors.push(errorMsg);
							this.logger.error(errorMsg);
						}
					}

					// Delete database record
					await this.analyticsDocumentRequestRepository.delete({ id: request.id });
					result.deletedRequests++;

					this.logger.log(`🗑️ Deleted expired analytics request: ${request.id} (expired: ${request.expiresAt})`);

				} catch (requestError) {
					const errorMsg = `Failed to cleanup request ${request.id}: ${requestError instanceof Error ? requestError.message : String(requestError)}`;
					result.errors.push(errorMsg);
					this.logger.error(errorMsg);
				}
			}

			this.logger.log('=== ✅ ANALYTICS DOCUMENT CLEANUP COMPLETED ===', {
				deletedRequests: result.deletedRequests,
				deletedFiles: result.deletedFiles,
				errors: result.errors.length
			});

		} catch (error) {
			const errorMsg = `Analytics document cleanup failed: ${error instanceof Error ? error.message : String(error)}`;
			result.errors.push(errorMsg);
			this.logger.error('=== ❌ ANALYTICS DOCUMENT CLEANUP FAILED ===', { error: errorMsg });
		}

		return result;
	}

	/**
	 * Get analytics document cleanup metrics for monitoring
	 */
	async getCleanupMetrics(): Promise<{
		totalRequests: number;
		expiredRequests: number;
		requestsByStatus: Record<string, number>;
		oldestRequest: Date | null;
		newestRequest: Date | null;
	}> {
		try {
			// Get total count
			const totalRequests = await this.analyticsDocumentRequestRepository.count();

			// Get expired count
			const expiredRequests = await this.analyticsDocumentRequestRepository.count({
				where: {
					expiresAt: LessThan(new Date())
				}
			});

			// Get requests by status
			const statusCounts = await this.analyticsDocumentRequestRepository
				.createQueryBuilder('request')
				.select('request.status', 'status')
				.addSelect('COUNT(*)', 'count')
				.groupBy('request.status')
				.getRawMany();

			const requestsByStatus: Record<string, number> = {};
			statusCounts.forEach(item => {
				requestsByStatus[item.status] = parseInt(item.count);
			});

			// Get oldest and newest requests
			const oldestRequest = await this.analyticsDocumentRequestRepository.findOne({
				order: { createdAt: 'ASC' },
				select: ['createdAt']
			});

			const newestRequest = await this.analyticsDocumentRequestRepository.findOne({
				order: { createdAt: 'DESC' },
				select: ['createdAt']
			});

			return {
				totalRequests,
				expiredRequests,
				requestsByStatus,
				oldestRequest: oldestRequest?.createdAt || null,
				newestRequest: newestRequest?.createdAt || null
			};

		} catch (error) {
			this.logger.error('Failed to get cleanup metrics', {
				error: error instanceof Error ? error.message : String(error)
			});
			throw error;
		}
	}

	/**
	 * Force cleanup of specific analytics document request
	 * Useful for manual cleanup or testing
	 */
	async forceCleanupRequest(requestId: string): Promise<boolean> {
		try {
			const request = await this.analyticsDocumentRequestRepository.findOne({
				where: { id: requestId },
				select: ['id', 'pdfFileKey', 'excelFileKey', 'clinicId', 'documentType']
			});

			if (!request) {
				this.logger.warn(`Analytics document request not found for cleanup: ${requestId}`);
				return false;
			}

			// Delete S3 files if they exist
			const filesToDelete = [request.pdfFileKey, request.excelFileKey].filter((key): key is string => Boolean(key));

			for (const fileKey of filesToDelete) {
				try {
					await this.s3Service.deleteFile(fileKey);
					this.logger.log(`🗑️ Force deleted S3 file: ${fileKey}`);
				} catch (fileError) {
					this.logger.error(`Failed to force delete S3 file ${fileKey}`, {
						error: fileError instanceof Error ? fileError.message : String(fileError)
					});
				}
			}

			// Delete database record
			await this.analyticsDocumentRequestRepository.delete({ id: requestId });

			this.logger.log(`🗑️ Force deleted analytics request: ${requestId}`);
			return true;

		} catch (error) {
			this.logger.error(`Failed to force cleanup request ${requestId}`, {
				error: error instanceof Error ? error.message : String(error)
			});
			return false;
		}
	}
}
