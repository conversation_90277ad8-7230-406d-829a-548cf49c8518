import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateAnalyticsDocumentRequestsTable1752667379441
	implements MigrationInterface
{
	public async up(queryRunner: QueryRunner): Promise<void> {
		// Create enum types
		await queryRunner.query(`
			CREATE TYPE "analytics_document_status_enum" AS ENUM (
				'PENDING',
				'PROCESSING', 
				'COMPLETED',
				'FAILED',
				'EXPIRED'
			)
		`);

		await queryRunner.query(`
			CREATE TYPE "analytics_document_type_enum" AS ENUM (
				'INVOICE',
				'RECEIPT',
				'CREDIT_NOTE'
			)
		`);

		await queryRunner.query(`
			CREATE TYPE "analytics_recipient_type_enum" AS ENUM (
				'CLIENT',
				'OTHER'
			)
		`);

		// Create the table
		await queryRunner.createTable(
			new Table({
				name: 'analytics_document_requests',
				columns: [
					{
						name: 'id',
						type: 'uuid',
						isPrimary: true,
						generationStrategy: 'uuid',
						default: 'uuid_generate_v4()'
					},
					{
						name: 'clinic_id',
						type: 'uuid',
						isNullable: false
					},
					{
						name: 'brand_id',
						type: 'uuid',
						isNullable: false
					},
					{
						name: 'user_id',
						type: 'uuid',
						isNullable: false
					},
					{
						name: 'document_type',
						type: 'analytics_document_type_enum',
						isNullable: false
					},
					{
						name: 'recipient_type',
						type: 'analytics_recipient_type_enum',
						isNullable: false
					},
					{
						name: 'recipient_email',
						type: 'varchar',
						isNullable: true
					},
					{
						name: 'recipient_phone',
						type: 'varchar',
						isNullable: true
					},
					{
						name: 'start_date',
						type: 'date',
						isNullable: false
					},
					{
						name: 'end_date',
						type: 'date',
						isNullable: false
					},
					{
						name: 'status',
						type: 'analytics_document_status_enum',
						default: "'PENDING'",
						isNullable: false
					},
					{
						name: 'pdf_file_key',
						type: 'varchar',
						isNullable: true
					},
					{
						name: 'excel_file_key',
						type: 'varchar',
						isNullable: true
					},
					{
						name: 'error_message',
						type: 'text',
						isNullable: true
					},
					{
						name: 'document_count',
						type: 'integer',
						default: 0,
						isNullable: false
					},
					{
						name: 'processing_metadata',
						type: 'jsonb',
						isNullable: true
					},
					{
						name: 'expires_at',
						type: 'timestamp',
						default: "CURRENT_TIMESTAMP + INTERVAL '7 days'",
						isNullable: false
					},
					{
						name: 'created_at',
						type: 'timestamp',
						default: 'CURRENT_TIMESTAMP',
						isNullable: false
					},
					{
						name: 'updated_at',
						type: 'timestamp',
						default: 'CURRENT_TIMESTAMP',
						isNullable: false
					}
				]
			}),
			true
		);

		// Create indexes
		await queryRunner.createIndex(
			'analytics_document_requests',
			new TableIndex({
				name: 'IDX_analytics_document_requests_clinic_created',
				columnNames: ['clinic_id', 'created_at']
			})
		);

		await queryRunner.createIndex(
			'analytics_document_requests',
			new TableIndex({
				name: 'IDX_analytics_document_requests_status_created',
				columnNames: ['status', 'created_at']
			})
		);

		await queryRunner.createIndex(
			'analytics_document_requests',
			new TableIndex({
				name: 'IDX_analytics_document_requests_expires_at',
				columnNames: ['expires_at']
			})
		);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		// Drop indexes
		await queryRunner.dropIndex(
			'analytics_document_requests',
			'IDX_analytics_document_requests_expires_at'
		);
		await queryRunner.dropIndex(
			'analytics_document_requests',
			'IDX_analytics_document_requests_status_created'
		);
		await queryRunner.dropIndex(
			'analytics_document_requests',
			'IDX_analytics_document_requests_clinic_created'
		);

		// Drop table
		await queryRunner.dropTable('analytics_document_requests');

		// Drop enum types
		await queryRunner.query(
			'DROP TYPE "analytics_recipient_type_enum"'
		);
		await queryRunner.query('DROP TYPE "analytics_document_type_enum"');
		await queryRunner.query('DROP TYPE "analytics_document_status_enum"');
	}
}
